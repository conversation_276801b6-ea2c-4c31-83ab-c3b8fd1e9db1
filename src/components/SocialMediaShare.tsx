"use client";

import React from 'react';
import { motion } from 'framer-motion';

interface SocialMediaShareProps {
  /** The meme image as a data URL or blob URL */
  imageUrl?: string;
  /** Text to share along with the image */
  shareText?: string;
  /** URL to share (defaults to current page) */
  shareUrl?: string;
  /** Custom CSS classes */
  className?: string;
}

export default function SocialMediaShare({
  imageUrl,
  shareText = "Check out this awesome meme I created! 🔥",
  shareUrl,
  className = ""
}: SocialMediaShareProps) {
  // Generate sharing text
  const generateShareText = () => {
    return `${shareText} 🎨 Created with Sybau Meme Generator`;
  };

  // Get current page URL dynamically
  const getCurrentUrl = () => {
    if (typeof window !== 'undefined') {
      return shareUrl || window.location.href;
    }
    return shareUrl || 'https://sybaumeme.com';
  };

  // Share to Twitter/X
  const shareToTwitter = () => {
    const text = generateShareText();
    const url = getCurrentUrl();
    const hashtags = 'meme,SybauMeme,MemeGenerator';

    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}&hashtags=${hashtags}`;
    window.open(twitterUrl, '_blank');
  };

  // Share to Facebook
  const shareToFacebook = () => {
    const url = getCurrentUrl();
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
    window.open(facebookUrl, '_blank');
  };

  // Share to WhatsApp
  const shareToWhatsApp = () => {
    const text = generateShareText();
    const url = getCurrentUrl();
    const message = `${text} ${url}`;

    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  // Share to Reddit
  const shareToReddit = () => {
    const text = generateShareText();
    const url = getCurrentUrl();
    const title = `Check out this awesome meme I created!`;

    const redditUrl = `https://www.reddit.com/submit?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}&text=${encodeURIComponent(text)}`;
    window.open(redditUrl, '_blank');
  };

  // Share to Telegram
  const shareToTelegram = () => {
    const text = generateShareText();
    const url = getCurrentUrl();

    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
    window.open(telegramUrl, '_blank');
  };

  // Share to LINE
  const shareToLine = () => {
    const text = generateShareText();
    const url = getCurrentUrl();

    const lineUrl = `https://social-plugins.line.me/lineit/share?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
    window.open(lineUrl, '_blank');
  };

  return (
    <motion.div
      className={`flex justify-center space-x-2 flex-wrap gap-y-2 ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <FacebookShareButton
          url={finalShareUrl}
          hashtag="#meme"
          onClick={handleShare}
        >
          <FacebookIcon size={iconSize} round />
        </FacebookShareButton>
      </motion.div>

      <motion.div variants={itemVariants}>
        <TwitterShareButton
          url={finalShareUrl}
          title={shareTextWithImage}
          onClick={handleShare}
        >
          <TwitterIcon size={iconSize} round />
        </TwitterShareButton>
      </motion.div>

      <motion.div variants={itemVariants}>
        <RedditShareButton
          url={finalShareUrl}
          title={shareTextWithImage}
          onClick={handleShare}
        >
          <RedditIcon size={iconSize} round />
        </RedditShareButton>
      </motion.div>

      <motion.div variants={itemVariants}>
        <WhatsappShareButton
          url={finalShareUrl}
          title={shareTextWithImage}
          onClick={handleShare}
        >
          <WhatsappIcon size={iconSize} round />
        </WhatsappShareButton>
      </motion.div>

      <motion.div variants={itemVariants}>
        <TelegramShareButton
          url={finalShareUrl}
          title={shareTextWithImage}
          onClick={handleShare}
        >
          <TelegramIcon size={iconSize} round />
        </TelegramShareButton>
      </motion.div>

      <motion.div variants={itemVariants}>
        <WeiboShareButton
          url={finalShareUrl}
          title={shareTextWithImage}
          onClick={handleShare}
        >
          <WeiboIcon size={iconSize} round />
        </WeiboShareButton>
      </motion.div>
    </motion.div>
  );
}
